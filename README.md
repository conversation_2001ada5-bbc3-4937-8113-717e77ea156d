# Jira Epic Time Tracker

A command line tool that analyzes time tracking data for Jira epics using Tempo. Get detailed summaries of how much time each employee worked on tasks under a specific epic for a given month.

## Features

- 📊 Analyze time spent on all tasks under a Jira epic
- 📅 Filter by specific month and year
- 👥 Break down time by employee for each task
- 📈 Provide summary statistics and totals
- 🔐 Secure API key management via environment variables

## Epics list

| Epic Key  | Epic Name |
|-----------|-------------|
| ST1-3752  | Bergets Ro |
| WP-3      | Solvalla |
| SWEBIO-75 | Sweden Bio |
| COBS-387  | Cobs |
| WP-1749   | Kraftstaden |
| WP-1791   | Vårvik |
| WP-1575   | Swedac |

## Sample Output

```
📊 Time Summary for Epic WP-1575 - June 2024
============================================================

🎫 WP-1576 - 5h 30m
   Implement user authentication system
   (<PERSON> - 3h 20m, <PERSON> Smith - 2h 10m)

🎫 WP-1577 - 2h 45m
   Create login page UI
   (<PERSON> - 2h 45m)

📈 Summary
--------------------
Total Tasks: 2
Total Time: 8h 15m
Total Employees: 2

👥 Employee Totals:
   Jane Smith: 4h 55m
   John Doe: 3h 20m
```

## 🚀 Quick Start

1. **Create virtual environment:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On macOS/Linux
   # or venv\Scripts\activate on Windows
   ```

2. **Setup:**
   ```bash
   python3 setup.py
   ```

3. **Configure API keys:**
   ```bash
   # Edit .env file with your credentials
   nano .env
   ```

4. **Test setup:**
   ```bash
   python3 test_setup.py
   ```

5. **Use the tool:**
   ```bash
   # Analyze epic WP-1575 for June
   python3 main.py WP-1575 6

   # With specific year and verbose output
   python3 main.py WP-1575 6 --year 2023 --verbose
   ```

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd jira-task-time
```

2. Create and activate a Python virtual environment:
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate
```

3. Install Python dependencies:
```bash
pip3 install -r requirements.txt
```

4. Set up your API credentials (see Configuration section below)

## Configuration

### Required API Keys

You need to obtain API keys for both Jira and Tempo:

#### 1. Jira API Token

**How to get it:**
1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click "Create API token"
3. Give it a label (e.g., "Jira Epic Time Tracker")
4. Copy the generated token

**What you need:**
- `JIRA_BASE_URL`: Your Jira instance URL (e.g., `https://yourcompany.atlassian.net`)
- `JIRA_EMAIL`: Your Jira account email address
- `JIRA_API_TOKEN`: The API token you just created

#### 2. Tempo API Token (OAuth 2.0)

**How to get it:**
1. Log into your Jira instance
2. Go to Tempo → Settings
3. Scroll down to "Data Access" and select "API integration"
4. Click "New Token" to generate a Tempo OAuth 2.0 token
5. Give it a name (e.g., "Epic Time Tracker")
6. **Configure permissions** (see required permissions below)
7. Copy the generated token

**Required Permissions:**
Select **"Custom Access"** and enable:
- ☑️ **View worklogs** (under "Worklogs scope") - Essential for reading worklog entries
- ☑️ **View projects** (under "Projects scope") - Recommended for project access
- ☑️ **View teams** (under "Teams scope") - Optional, helps with employee names

**Minimum Required:** Just "View worklogs" is sufficient for core functionality.

**Important:** Tempo API v4 uses OAuth 2.0 Bearer tokens, not simple API keys.

**What you need:**
- `TEMPO_API_TOKEN`: The Tempo API token with worklog read permissions

### Environment Setup

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` with your actual values:
```bash
# Jira Configuration
JIRA_BASE_URL=https://yourcompany.atlassian.net
JIRA_EMAIL=<EMAIL>
JIRA_API_TOKEN=your_jira_api_token_here

# Tempo Configuration  
TEMPO_API_TOKEN=your_tempo_api_token_here
```

## Usage

### Basic Usage

**Important:** Make sure your virtual environment is activated before running commands:
```bash
source venv/bin/activate  # On macOS/Linux
# or venv\Scripts\activate on Windows
```

```bash
python3 main.py <EPIC_KEY> <MONTH>
```

**Examples:**
```bash
# Analyze epic WP-1575 for June (current year)
python3 main.py WP-1575 6

# Analyze epic PROJ-123 for December 2023
python3 main.py PROJ-123 12 --year 2023

# Enable verbose output for debugging
python3 main.py WP-1575 6 --verbose
```

### Parameters

- `EPIC_KEY`: The Jira epic key (e.g., `WP-1575`, `PROJ-123`)
- `MONTH`: Month number (1-12, where 1=January, 6=June, 12=December)
- `--year`: Optional year (defaults to current year)
- `--verbose`: Enable detailed output for debugging

## Troubleshooting

### Common Issues

**"Epic not found or not accessible"**
- Verify the epic key is correct
- Ensure your Jira account has access to the epic
- Check that the issue is actually an Epic type

**"Missing required environment variables"**
- Make sure your `.env` file exists and has all required variables
- Check that there are no extra spaces around the `=` signs
- Verify your API tokens are correct

**"Failed to fetch worklogs", "401 Unauthorized", or "400 Bad Request"**
- **401 Unauthorized**: Check your Tempo API token and permissions
- **400 Bad Request**: Usually means incorrect API parameters (fixed in latest version)
- Verify your `TEMPO_BASE_URL` matches your region:
  - Global: `https://api.tempo.io/4`
  - EU: `https://api.eu.tempo.io/4`
- Ensure Tempo is installed and configured in your Jira instance
- Check that you have permission to view time tracking data
- Verify the token has "View worklogs" permission enabled
- Make sure you have access to the projects containing the epic's issues

**Note**: This tool uses Tempo API v4 which requires issue IDs (not keys). The tool automatically converts issue keys to IDs.

**"No time logged for epic"**
- Verify that time was actually logged in Tempo for that month
- Check that the date range is correct
- Ensure worklogs are associated with the correct issues

### Testing Your Setup

You can test your configuration by running:
```bash
# Make sure virtual environment is activated first
source venv/bin/activate

# Test configuration
python3 -c "from config import get_config; print('Configuration loaded successfully')"

# Or run the comprehensive test script
python3 test_setup.py
```

## Requirements

- Python 3.7+
- Jira Cloud or Server instance
- Tempo add-on installed in Jira
- Valid API tokens for both Jira and Tempo

**Note:** This tool uses an isolated Python virtual environment to avoid conflicts with other Python projects on your system.

## Dependencies

- `requests`: HTTP client for API calls
- `click`: Command line interface framework
- `python-dateutil`: Date parsing and manipulation
- `python-dotenv`: Environment variable management

## License

This project is provided as-is for internal use. Please ensure compliance with your organization's API usage policies.
